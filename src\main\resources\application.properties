spring.application.name=isp392

spring.devtools.add-properties=true
spring.devtools.livereload.enabled=true

# MS SQL Server Configuration
spring.datasource.url=********************************************************************************************
spring.datasource.username=sa
spring.datasource.password=123
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect

# Optional: If you want to see the SQL queries Hibernate generates
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

# Hibernate ddl auto (create, create-drop, validate, update)
# Set to 'create' for first run to initialize database tables, then change to 'update' for production
spring.jpa.hibernate.ddl-auto=none

# Spring Security settings
# Disable for development only - enable in production
spring.security.debug=true

#Debug
logging.level.root=INFO

# Temporary fix for circular dependency
# This should be removed once the code structure is improved
spring.main.allow-circular-references=true

# Google OAuth2 Client Configuration
# Replace these with actual values from Google Developer Console
spring.security.oauth2.client.registration.google.client-id=client-id
spring.security.oauth2.client.registration.google.client-secret=client-secret
spring.security.oauth2.client.registration.google.scope=email,profile
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}

# Email Configuration for OTP Password Reset
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>      
spring.mail.password=......
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# OTP Configuration
app.otp.expiration-minutes=5
app.otp.max-attempts=3
app.base-url=http://localhost:8080

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.prefix=classpath:/templates/

# Cấu hình để đảm bảo Thymeleaf sử dụng Java 8 Date/Time API
spring.thymeleaf.enable-spring-el-compiler=true
spring.mvc.format.date=yyyy-MM-dd
spring.mvc.format.date-time=yyyy-MM-dd HH:mm:ss
spring.mvc.format.time=HH:mm:ss

# Server Configuration
server.servlet.session.persistent=false

# Configure MIME types
spring.mvc.contentnegotiation.favor-parameter=true
spring.mvc.contentnegotiation.media-types.js=application/javascript
spring.mvc.contentnegotiation.media-types.css=text/css

# Configure static resource handling
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
spring.web.resources.cache.cachecontrol.max-age=365d

# Configure server to handle large file uploads
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configure chunked encoding
server.compression.enabled=true
server.compression.mime-types=application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain
server.compression.min-response-size=2048

# Fix for chunked encoding issues
server.tomcat.max-http-form-post-size=20MB
server.tomcat.max-swallow-size=20MB
server.tomcat.connection-timeout=60000
spring.mvc.async.request-timeout=60000
server.connection-timeout=60000
server.http2.enabled=true
server.tomcat.use-chunked-encoding=false

# Platform commission rate (10% by default)
platform.commission.rate=0.10

# Application Configuration
server.port=8080

# Dùng API Key miễn phí từ Google AI Studio
spring.ai.vertex.ai.gemini.api-key=AIzaSyA2_Sa_8Fj4pSE3_z5CQFfaLfZLG1R11ys

# Các tùy chọn khác vẫn giữ nguyên
spring.ai.vertex.ai.gemini.chat.options.model=gemini-1.5-flash
# Google Vertex AI Embeddings Configuration
spring.ai.vertex.ai.gemini.embedding.options.model=text-embedding-004
spring.ai.vertex.ai.gemini.embedding.options.dimensions=768
spring.ai.vertex.ai.gemini.embedding.enabled=true

# Alternative: OpenAI Embeddings (comment out Vertex AI if using this)
# spring.ai.openai.embedding.options.model=text-embedding-3-small
# spring.ai.openai.embedding.options.dimensions=1536
# spring.ai.openai.api-key=${OPENAI_API_KEY:your-openai-api-key}
spring.ai.vertex.ai.gemini.chat.options.temperature=0.7

spring.ai.vertex.ai.gemini.project-id=gen-lang-client-0274098971
spring.ai.vertex.ai.gemini.location=us-central1
# spring.ai.vertex.ai.gemini.chat.options.model=gemini-1.5-flash
# spring.ai.vertex.ai.gemini.chat.options.temperature=0.7

# Simple Vector Store Configuration
spring.ai.vectorstore.simple.enabled=true
spring.ai.vectorstore.simple.path=data/vector-store.json

# Disable auto-configurations that are not needed
spring.autoconfigure.exclude=org.springframework.ai.autoconfigure.vertexai.embedding.VertexAiEmbeddingAutoConfiguration,org.springframework.ai.autoconfigure.vertexai.VertexAiGeminiChatAutoConfiguration

# Chat Configuration
chat.max-history-messages=20
chat.session-timeout-hours=24
chat.enable-rag=true
chat.max-results=5

# VNPay Configuration
vnpay.terminal-id=4YUP19I4
vnpay.secret-key=MDUIFDCRAKLNBPOFIAFNEKFRNMFBYEPX
vnpay.payment-url=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
vnpay.api-url=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction
app.return-url=http://localhost:8080/buyer/vnpay-return
