<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Post - ReadHub</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/style.css}">

</head>
<body>

<div th:replace="~{fragments/header :: header-content}"></div>

<main class="blog-edit-page py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <header class="text-center mb-5">
                    <h1 class="display-5">Edit Your Post</h1>
                    <p class="text-muted">Refine your thoughts and update your story.</p>
                </header>

                <div th:if="${error}" class="alert alert-danger" role="alert" th:text="${error}"></div>
                <div th:if="${success}" class="alert alert-success" role="alert" th:text="${success}"></div>

                <form th:action="@{/blog/edit/{id}(id=${blog.blogId})}"
                      th:object="${blog}"
                      method="post"
                      enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="title" class="form-label fs-5">Post Title</label>
                        <input type="text" class="form-control form-control-lg" id="title" th:field="*{title}" required>
                    </div>
                    <div class="mb-4">
                        <label for="imageFile" class="form-label fs-5">Thumbnail Image</label>
                        <input type="file" class="form-control" id="imageFile" name="imageFile" accept="image/*">
                        <small class="text-muted">Accepted formats: jpg, png, webp. Max: 5MB</small>
                    </div>
                    <div th:if="${blog.imageUrl != null}">
                        <p class="fw-bold">Current Image:</p>
                        <img th:src="@{${blog.imageUrl}}" alt="Blog Thumbnail" class="img-thumbnail mb-2" style="max-width: 300px;">
                    </div>
                    <div class="mb-4">
                        <label for="content" class="form-label fs-5">Content</label>
                        <textarea class="form-control" id="content" th:field="*{content}" rows="15" required></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a th:href="@{/blog/{id}(id=${blog.blogId})}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Post</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<div th:replace="~{fragments/footer :: footer-content}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>